# @kdt310722/utils

[![npm version][npm-version-src]][npm-version-href]
[![npm downloads][npm-downloads-src]][npm-downloads-href]
[![ci][ci-src]][ci-href]
[![issues][issues-src]][issues-href]
[![license][license-src]][license-href]

A collection of utility functions for JavaScript / TypeScript applications.

## Usage

Install package:

```sh
# npm
npm install @kdt310722/utils

# yarn
yarn add @kdt310722/utils

# pnpm
pnpm install @kdt310722/utils

# bun
bun install @kdt310722/utils
```

Import:

```js
// ESM
import {} from '@kdt310722/utils'

// CommonJS
const {} = require('@kdt310722/utils')
```

## License

Published under [MIT License](LICENSE.md).

<!-- Badges -->

[npm-version-src]: https://img.shields.io/npm/v/@kdt310722/utils?style=flat&colorA=1B3C4A&colorB=32A9C3&label=version
[npm-version-href]: https://npmjs.com/package/@kdt310722/utils
[npm-downloads-src]: https://img.shields.io/npm/dm/@kdt310722/utils?style=flat&colorA=1B3C4A&colorB=32A9C3&label=downloads
[npm-downloads-href]: https://npmjs.com/package/@kdt310722/utils
[ci-src]: https://img.shields.io/github/actions/workflow/status/kdt310722/utils/ci.yml?style=flat&colorA=1B3C4A&colorB=32A9C3&label=ci
[ci-href]: https://github.com/kdt310722/utils/actions/workflows/ci.yml
[issues-src]: https://img.shields.io/github/issues/kdt310722/utils?style=flat&colorA=1B3C4A&colorB=32A9C3&label=issues
[issues-href]: https://github.com/kdt310722/utils/issues
[license-src]: https://img.shields.io/npm/l/@kdt310722/utils?style=flat&colorA=1B3C4A&colorB=32A9C3&label=license
[license-href]: https://github.com/@kdt310722/utils/blob/main/LICENSE.md
