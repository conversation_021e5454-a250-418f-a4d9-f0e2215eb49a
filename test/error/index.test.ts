import { describe, expect, test } from 'vitest'
import * as errorModule from '../../src/error'
import { BaseError, type BaseErrorOptions, type ErrorCode } from '../../src/error/base-error'
import { buildCauseChain, formatCause } from '../../src/error/causes'
import { isAbortError } from '../../src/error/errors'
import { stringifyError, type ErrorStringifyOptions } from '../../src/error/stringify'

describe('error module exports', () => {
    describe('module structure', () => {
        test('should export all expected functions and classes', () => {
            expect(errorModule.BaseError).toBeDefined()
            expect(errorModule.buildCauseChain).toBeDefined()
            expect(errorModule.formatCause).toBeDefined()
            expect(errorModule.isAbortError).toBeDefined()
            expect(errorModule.stringifyError).toBeDefined()
        })

        test('should export BaseError class', () => {
            expect(errorModule.BaseError).toBe(BaseError)
            expect(typeof errorModule.BaseError).toBe('function')
            expect(errorModule.BaseError.prototype).toBeInstanceOf(Error)
        })

        test('should export buildCauseChain function', () => {
            expect(errorModule.buildCauseChain).toBe(buildCauseChain)
            expect(typeof errorModule.buildCauseChain).toBe('function')
        })

        test('should export formatCause function', () => {
            expect(errorModule.formatCause).toBe(formatCause)
            expect(typeof errorModule.formatCause).toBe('function')
        })

        test('should export isAbortError function', () => {
            expect(errorModule.isAbortError).toBe(isAbortError)
            expect(typeof errorModule.isAbortError).toBe('function')
        })

        test('should export stringifyError function', () => {
            expect(errorModule.stringifyError).toBe(stringifyError)
            expect(typeof errorModule.stringifyError).toBe('function')
        })
    })

    describe('type exports', () => {
        test('should have ErrorCode type available', () => {
            const stringCode: ErrorCode = 'STRING_CODE'
            const numberCode: ErrorCode = 500
            const symbolCode: ErrorCode = Symbol('symbol')

            expect(typeof stringCode).toBe('string')
            expect(typeof numberCode).toBe('number')
            expect(typeof symbolCode).toBe('symbol')
        })

        test('should have BaseErrorOptions type available', () => {
            const options: BaseErrorOptions = {
                name: 'CustomError',
                code: 'CUSTOM_CODE',
                retryable: true,
                cause: new Error('Original error'),
            }

            expect(options.name).toBe('CustomError')
            expect(options.code).toBe('CUSTOM_CODE')
            expect(options.retryable).toBe(true)
            expect(options.cause).toBeInstanceOf(Error)
        })

        test('should have ErrorStringifyOptions type available', () => {
            const options: ErrorStringifyOptions = {
                includeCode: true,
                includeCause: true,
                maxCauseDepth: 5,
            }

            expect(options.includeCode).toBe(true)
            expect(options.includeCause).toBe(true)
            expect(options.maxCauseDepth).toBe(5)
        })
    })

    describe('integration between exported functions', () => {
        class TestError extends errorModule.BaseError {
            constructor(message?: string, code?: string, cause?: unknown) {
                super(message, { code, cause })
            }
        }

        test('should work together in error creation and formatting', () => {
            const cause = new Error('Original error')
            const error = new TestError('Main error', 'MAIN_CODE', cause)

            const stringified = errorModule.stringifyError(error)
            expect(stringified).toContain('[MAIN_CODE] TestError: Main error')
            expect(stringified).toContain('Caused by: Error: Original error')
        })

        test('should work with cause chain building', () => {
            const rootCause = new Error('Root cause')
            const middleCause = new Error('Middle cause', { cause: rootCause })
            const topError = new TestError('Top error', 'TOP_CODE', middleCause)

            const causeChain = errorModule.buildCauseChain(topError.cause, 5)
            expect(causeChain).toContain('Error: Middle cause')
            expect(causeChain).toContain('Error: Root cause')
        })

        test('should work with cause formatting', () => {
            const visited = new WeakSet()
            const error = new TestError('Test error', 'TEST_CODE')

            const formatted = errorModule.formatCause(error, visited, true)
            expect(formatted).toBe('[TEST_CODE] TestError: Test error')
        })

        test('should work with abort error detection', () => {
            const abortError = new DOMException('Operation aborted', 'AbortError')
            const regularError = new Error('Regular error')

            expect(errorModule.isAbortError(abortError)).toBe(true)
            expect(errorModule.isAbortError(regularError)).toBe(false)
        })
    })

    describe('complete workflow scenarios', () => {
        test('should handle complete error creation and serialization workflow', () => {
            class CustomError extends errorModule.BaseError {
                constructor(message?: string, code?: string, cause?: unknown) {
                    super(message, { code, cause })
                }
            }

            const originalError = new Error('Database connection failed')
            const serviceError = new CustomError('Service unavailable', 'SERVICE_ERROR', originalError)
            const apiError = new CustomError('API request failed', 'API_ERROR', serviceError)

            const serialized = apiError.toJSON()
            expect(serialized.name).toBe('CustomError')
            expect(serialized.message).toBe('API request failed')
            expect(serialized.code).toBe('API_ERROR')

            const stringified = apiError.toString()
            expect(stringified).toContain('[API_ERROR] CustomError: API request failed')
            expect(stringified).toContain('Caused by: [SERVICE_ERROR] CustomError: Service unavailable')
            expect(stringified).toContain('Caused by: Error: Database connection failed')
        })

        test('should handle error analysis workflow', () => {
            const errors: unknown[] = [
                new Error('Regular error'),
                new DOMException('Abort error', 'AbortError'),
                new errorModule.BaseError('Base error', { code: 'BASE_CODE' }),
                'String error',
                null,
                undefined,
            ]

            const analysis = errors.map((error) => ({
                isAbortError: errorModule.isAbortError(error),
                isBaseError: error instanceof errorModule.BaseError,
                formatted: error instanceof Error ? errorModule.stringifyError(error) : String(error),
            }))

            expect(analysis[0].isAbortError).toBe(false)
            expect(analysis[0].isBaseError).toBe(false)
            expect(analysis[0].formatted).toBe('Error: Regular error')

            expect(analysis[1].isAbortError).toBe(true)
            expect(analysis[1].isBaseError).toBe(false)
            expect(analysis[1].formatted).toBe('AbortError: Abort error')

            expect(analysis[2].isAbortError).toBe(false)
            expect(analysis[2].isBaseError).toBe(true)
            expect(analysis[2].formatted).toBe('[BASE_CODE] BaseError: Base error')

            expect(analysis[3].isAbortError).toBe(false)
            expect(analysis[3].isBaseError).toBe(false)
            expect(analysis[3].formatted).toBe('String error')
        })

        test('should handle error recovery workflow', () => {
            class RetryableError extends errorModule.BaseError {
                constructor(message?: string, code?: string, cause?: unknown, retryable = true) {
                    super(message, { code, cause, retryable })
                }
            }

            const networkError = new RetryableError('Network timeout', 'NETWORK_TIMEOUT', undefined, true)
            const validationError = new RetryableError('Invalid input', 'VALIDATION_ERROR', undefined, false)

            const shouldRetry = (error: unknown): boolean => {
                if (error instanceof errorModule.BaseError) {
                    return error.retryable === true
                }

                return errorModule.isAbortError(error) ? false : true
            }

            expect(shouldRetry(networkError)).toBe(true)
            expect(shouldRetry(validationError)).toBe(false)
            expect(shouldRetry(new DOMException('Aborted', 'AbortError'))).toBe(false)
            expect(shouldRetry(new Error('Unknown error'))).toBe(true)
        })
    })

    describe('module consistency', () => {
        test('should maintain consistent behavior across all exports', () => {
            const error = new errorModule.BaseError('Test error', { code: 'TEST_CODE' })

            const stringifyResult = errorModule.stringifyError(error)
            const toStringResult = error.toString()

            expect(stringifyResult).toBe(toStringResult)
        })

        test('should handle all error types consistently', () => {
            const errors = [
                new Error('Standard error'),
                new TypeError('Type error'),
                new errorModule.BaseError('Base error', { code: 'BASE' }),
                new DOMException('DOM error', 'AbortError'),
            ]

            for (const error of errors) {
                const stringified = errorModule.stringifyError(error)
                expect(typeof stringified).toBe('string')
                expect(stringified.length).toBeGreaterThan(0)
                expect(stringified).toContain(error.name)
                expect(stringified).toContain(error.message)
            }
        })

        test('should provide consistent type checking', () => {
            const values: unknown[] = [
                new errorModule.BaseError('Base error'),
                new Error('Regular error'),
                new DOMException('Abort', 'AbortError'),
                'string',
                42,
                null,
                undefined,
                {},
                [],
            ]

            for (const value of values) {
                const isBaseError = value instanceof errorModule.BaseError
                const isAbortError = errorModule.isAbortError(value)

                if (isBaseError) {
                    expect(value).toBeInstanceOf(Error)
                    expect(value).toBeInstanceOf(errorModule.BaseError)
                }

                if (isAbortError) {
                    expect(value).toBeInstanceOf(DOMException)
                    expect((value as DOMException).name).toBe('AbortError')
                }

                expect(typeof isBaseError).toBe('boolean')
                expect(typeof isAbortError).toBe('boolean')
            }
        })
    })

    describe('backwards compatibility', () => {
        test('should maintain API compatibility', () => {
            const requiredExports = [
                'BaseError',
                'buildCauseChain',
                'formatCause',
                'isAbortError',
                'stringifyError',
            ]

            for (const exportName of requiredExports) {
                expect(errorModule).toHaveProperty(exportName)
                expect((errorModule as any)[exportName]).toBeDefined()
            }
        })

        test('should support legacy usage patterns', () => {
            const { BaseError: LegacyBaseError, stringifyError: legacyStringify } = errorModule

            class LegacyError extends LegacyBaseError {
                constructor(message: string) {
                    super(message, { code: 'LEGACY' })
                }
            }

            const error = new LegacyError('Legacy error')
            const result = legacyStringify(error)

            expect(result).toBe('[LEGACY] LegacyError: Legacy error')
        })
    })
})
